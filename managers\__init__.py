"""
Central managers for the vending machine system.
Handles session management, WebSocket connections, error handling, and sequence orchestration.
"""

from .ws_manager import WebSocketManager, ws_manager
from .error_manager import <PERSON>rrorManager, error_manager, SectionError, ErrorType, ErrorCode
from .session_manager import SessionManager, session_manager, SessionType, SessionStatus, SectionConfig

# Note: sequence_manager is imported on-demand to avoid circular imports with hardware

__all__ = [
    "WebSocketManager", "ws_manager",
    "ErrorManager", "error_manager", "SectionError", "ErrorType", "ErrorCode", 
    "SessionManager", "session_manager", "SessionType", "SessionStatus", "SectionConfig"
] 