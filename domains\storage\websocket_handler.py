"""
WebSocket Handler for Storage Flow Sessions.
Handles WebSocket communication for flow-based storage operations.
"""

import logging
import json
from fastapi import WebSocket, WebSocketDisconnect
import asyncio

from .flow_coordinator import flow_coordinator
from managers.ws_manager import ws_manager

logger = logging.getLogger(__name__)

async def handle_storage_flow_websocket(
    websocket: WebSocket,
    session_id: str
):
    """Handle WebSocket communication for storage flow sessions"""
    
    logger.info(f"Storage flow WebSocket connected: {session_id}")
    
    try:
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for storage flow session: {session_id}")
        
        # This part is different from product flow, as we don't have a get_flow_status method
        # We can assume the flow exists if the session does.
        
        await ws_manager.send(session_id, {
            "type": "flow_status",
            "status": "connected",
            "message": "Připojeno k storage flow"
        })
        
        asyncio.create_task(flow_coordinator.execute_current_step_async(session_id))
        
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue
                
                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue
                
                msg_type = data.get("type")
                
                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue
                
                success = await flow_coordinator.handle_websocket_message(session_id, data)
                
                if not success:
                    await ws_manager.send(session_id, {
                        "type": "message_error",
                        "message": f"Failed to handle message type '{msg_type}'"
                    })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                await ws_manager.send(session_id, {"type": "error", "message": str(e)})
    
    except WebSocketDisconnect:
        logger.info(f"Storage flow WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in storage flow WebSocket handler: {e}")
    finally:
        ws_manager.disconnect(session_id)
        await flow_coordinator.cleanup_flow(session_id)
        logger.info(f"Storage flow WebSocket connection closed: {session_id}")


async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """Handle payment callback for storage flow sessions"""
    logger.info(f"Storage websocket handler: Handling payment callback for storage flow session {session_id}: {status}")

    try:
        # Forward to flow coordinator
        result = await flow_coordinator.handle_payment_callback(session_id, status, message)
        logger.info(f"Storage websocket handler: Payment callback result for {session_id}: {result}")
        return result

    except Exception as e:
        logger.error(f"Storage websocket handler: Error handling payment callback for storage flow session {session_id}: {e}")
        import traceback
        logger.error(f"Storage websocket handler: Traceback: {traceback.format_exc()}")
        return False
