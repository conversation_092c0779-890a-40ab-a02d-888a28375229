-- migrate:up
CREATE TABLE box_sections (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(255) NOT NULL,
    box_uuid VARCHAR(255) NOT NULL,
    section_id INT(11) NOT NULL,
    identification_name VARCHAR(255) NOT NULL,
    tempered INT(11) NOT NULL DEFAULT 0,
    visible INT(11) NOT NULL DEFAULT 1,
    blocked INT(11) NOT NULL DEFAULT 0,
    service INT(11) NOT NULL DEFAULT 0,
    title VARCHAR(255) DEFAULT NULL,
    lock_id VARCHAR(50) NOT NULL,
    led_section INT(11) NOT NULL,
    fixed_pin TINYINT(1) NOT NULL DEFAULT 0,
    pin VARCHAR(255) DEFAULT NULL,
    size_category INT(11) DEFAULT NULL,
    size_width INT(11) DEFAULT NULL,
    size_depth INT(11) DEFAULT NULL,
    size_height INT(11) DEFAULT NULL,
    mode VARCHAR(255) NOT NULL DEFAULT 'parcel',
    type VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE box_sections;
