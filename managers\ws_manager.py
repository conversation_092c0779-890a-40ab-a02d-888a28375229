from fastapi import WebSocket
from typing import Dict, <PERSON>, Any
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.active_sessions: Dict[str, bool] = {}

    async def connect(self, session_id: str, websocket: WebSocket):
        """Register a new WebSocket connection"""
        logger.info(f"Registering WebSocket connection for session: {session_id}")
        self.connections[session_id] = websocket
        self.active_sessions[session_id] = True

    def disconnect(self, session_id: str):
        """Remove a WebSocket connection"""
        logger.info(f"Removing WebSocket connection for session: {session_id}")
        self.connections.pop(session_id, None)
        self.active_sessions[session_id] = False

    async def send(self, session_id: str, message: Union[str, dict]) -> bool:
        """Send a message to a specific WebSocket connection"""
        if session_id not in self.connections:
            logger.warning(f"No WebSocket connection found for session: {session_id}")
            return False

        try:
            websocket = self.connections[session_id]
            if isinstance(message, dict):
                await websocket.send_json(message)
            else:
                await websocket.send_text(message)
            
            # Yield control to ensure message is sent
            await asyncio.sleep(0)
            return True
            
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            self.disconnect(session_id)
            return False

    def is_connected(self, session_id: str) -> bool:
        """Check if a session has an active WebSocket connection"""
        return session_id in self.connections

    async def broadcast(self, message: Union[str, dict]):
        """Send a message to all connected WebSocket clients"""
        disconnected = []
        for session_id in self.connections:
            success = await self.send(session_id, message)
            if not success:
                disconnected.append(session_id)
        
        # Clean up disconnected sessions
        for session_id in disconnected:
            self.disconnect(session_id)

    async def close_all(self):
        """Close all active WebSocket connections"""
        for session_id, websocket in self.connections.items():
            try:
                await websocket.close()
            except:
                pass
            self.disconnect(session_id)

# Global instance
ws_manager = WebSocketManager()