import logging
import j<PERSON>
from fastapi import WebSocket, WebSocketDisconnect
from typing import Op<PERSON>

from transaction.models import TransactionState, TransactionContext
from transaction.manager import transaction_manager
from managers.ws_manager import ws_manager

logger = logging.getLogger(__name__)

async def handle_transaction_websocket(
    websocket: WebSocket,
    session_id: str,
    context: Optional[TransactionContext] = None
):
    """Handle WebSocket communication for transactions"""
    
    logger.info(f"Transaction WebSocket connected: {session_id}")
    
    # Get or validate context
    try:
        if not context:
            context = transaction_manager.active_transactions.get(session_id)
            logger.info(f"Retrieved transaction context: {context}")
            
            if not context:
                logger.error(f"No active transaction found for session: {session_id}")
                await websocket.close(code=4000, reason="No active transaction")
                return
    except Exception as e:
        logger.error(f"Error getting transaction context: {e}")
        await websocket.close(code=4000, reason="Error getting transaction context")
        return
    
    try:
        # Register connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for session: {session_id}")
        
        # Send initial state
        initial_state = {
            "type": "transaction_state",
            "state": context.current_state.value,
            "data": {
                "payment_data": context.payment_data,
                "hardware_data": context.hardware_data,
                "metadata": context.metadata
            },
            "message": "Připojeno k transakci"
        }
        logger.info(f"Sending initial state: {initial_state}")
        
        await ws_manager.send(session_id, initial_state)
        
        # Start payment processing if in PAYMENT_PENDING state
        if context.current_state == TransactionState.PAYMENT_PENDING:
            logger.info(f"Starting payment processing for session {session_id}")
            try:
                success = await transaction_manager.start_payment_processing(session_id)
                
                if not success:
                    logger.error(f"Failed to start payment processing for session {session_id}")
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Nepodařilo se zahájit platbu"
                    })
                    return
                
                logger.info(f"Payment processing started successfully for session {session_id}")
                
            except Exception as e:
                logger.error(f"Error starting payment processing: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Chyba při zahájení platby: {str(e)}"
                })
                return
        
        # Main message loop
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                logger.debug(f"Received message: {message}")
                
                data = json.loads(message)
                msg_type = data.get("type")
                
                logger.info(f"Processing message type: {msg_type}")
                
                if msg_type == "ping":
                    await ws_manager.send(session_id, {
                        "type": "pong",
                        "message": "Connection alive"
                    })
                    continue
                
                # Handle state-specific messages
                if context.current_state == TransactionState.HARDWARE_READY:
                    if msg_type == "confirm_pickup":
                        logger.info(f"Processing hardware pickup confirmation for session {session_id}")
                        logger.info(f"Current context state: {context.current_state}")
                        logger.info(f"Payment data: {context.payment_data}")
                        
                        # Open the locker
                        success = await transaction_manager.open_locker_for_pickup(session_id)
                        if not success:
                            logger.error(f"Failed to open locker for session {session_id}")
                            await ws_manager.send(session_id, {
                                "type": "hardware_status",
                                "status": "error",
                                "message": "Nepodařilo se otevřít schránku"
                            })
                            break
                        
                        logger.info(f"Locker opening initiated for session {session_id}")
                        
                elif context.current_state == TransactionState.PAYMENT_PROCESSING:
                    if msg_type == "cancel_payment":
                        logger.info("Processing payment cancellation")
                        # Handle payment cancellation
                        context.current_state = TransactionState.ERROR
                        context.add_event("payment_cancelled")
                        
                        await ws_manager.send(session_id, {
                            "type": "payment_status",
                            "status": "cancelled",
                            "message": "Platba byla zrušena"
                        })
                        break
                else:
                    logger.info(f"Received message type '{msg_type}' in state '{context.current_state}' - no handler defined")
                
            except json.JSONDecodeError as e:
                logger.error(f"Invalid message format: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": "Neplatný formát zprávy"
                })
                continue
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Chyba při zpracování zprávy: {str(e)}"
                })
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in WebSocket handler: {e}")
        try:
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Neočekávaná chyba: {str(e)}"
            })
        except:
            pass
    finally:
        # Cleanup
        ws_manager.disconnect(session_id)
        logger.info(f"WebSocket connection closed: {session_id}") 