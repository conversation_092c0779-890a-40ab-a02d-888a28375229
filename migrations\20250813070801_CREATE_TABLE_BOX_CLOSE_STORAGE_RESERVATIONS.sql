-- migrate:up
CREATE TABLE storage_reservations (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    box_uuid VARCHAR(255) DEFAULT NULL,
    section_id VARCHAR(255) DEFAULT NULL,
    status INT(11) NOT NULL DEFAULT 0,
    reservation_pin VARCHAR(255) DEFAULT NULL,
    email VARCHAR(255) DEFAULT NULL,
    price DOUBLE NOT NULL DEFAULT 0,
    paid_status VARCHAR(255) DEFAULT NULL,
    category INT(11) DEFAULT NULL,
    last_update TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE storage_reservations;
