#!/usr/bin/env python3
"""
Order Module Testing Script

This script tests all order module functionality as described in task.py:
- Employment courier endpoints (pickup-expired, pickup, deliver)
- Employment customer endpoints (send, pickup, reclaim)
- WebSocket communication for all flows
- Response validation with valid/invalid inputs

Valid test data:
- Phone numbers: 123456789, 987654321, 555666777
- Reclamation PINs: RECLAIM123, RECLAIM456
"""

import asyncio
import json
import logging
import requests
import websockets
from typing import Dict, Any, List
import time
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OrderModuleTester:
    def __init__(self, base_url: str = "http://localhost:8000", ws_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.ws_url = ws_url
        self.test_results = []
        
        # Test data from task.py
        self.valid_phones = ["123456789", "987654321", "555666777"]
        self.invalid_phones = ["000000000", "111111111", "invalid_phone"]
        self.valid_reclaim_pins = ["RECLAIM123", "RECLAIM456"]
        self.invalid_reclaim_pins = ["INVALID123", "WRONG456", ""]
        self.valid_pickup_pins = ["123456", "789012"]  # These would be generated by the system
        self.invalid_pickup_pins = ["000000", "invalid", ""]
        
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        logger.info(f"{status} - {test_name}: {details}")
        
    async def test_endpoint(self, endpoint: str, payload: Dict[str, Any], expected_fields: List[str]) -> Dict[str, Any]:
        """Test HTTP endpoint and return response"""
        try:
            url = f"{self.base_url}{endpoint}"
            logger.info(f"Testing endpoint: {endpoint}")
            logger.info(f"Payload: {payload}")
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code != 200:
                self.log_test_result(f"HTTP {endpoint}", False, f"Status code: {response.status_code}")
                return {}
                
            data = response.json()
            logger.info(f"Response: {data}")
            
            # Validate required fields
            missing_fields = [field for field in expected_fields if field not in data]
            if missing_fields:
                self.log_test_result(f"HTTP {endpoint}", False, f"Missing fields: {missing_fields}")
                return data
                
            self.log_test_result(f"HTTP {endpoint}", True, f"Success: {data.get('success', False)}")
            return data
            
        except Exception as e:
            self.log_test_result(f"HTTP {endpoint}", False, f"Exception: {str(e)}")
            return {}
    
    async def test_websocket_communication(self, session_id: str, test_messages: List[Dict[str, Any]]) -> bool:
        """Test WebSocket communication for a session"""
        if not session_id:
            self.log_test_result("WebSocket", False, "No session_id provided")
            return False
            
        try:
            ws_endpoint = f"{self.ws_url}/ws/{session_id}"
            logger.info(f"Connecting to WebSocket: {ws_endpoint}")
            
            async with websockets.connect(ws_endpoint) as websocket:
                # Wait for initial connection message
                try:
                    initial_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    logger.info(f"Initial WebSocket message: {initial_msg}")
                except asyncio.TimeoutError:
                    logger.warning("No initial WebSocket message received")
                
                # Send test messages
                for msg in test_messages:
                    logger.info(f"Sending WebSocket message: {msg}")
                    await websocket.send(json.dumps(msg))
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        logger.info(f"WebSocket response: {response}")
                    except asyncio.TimeoutError:
                        logger.warning(f"No response to message: {msg}")
                
                # Send ping to test connection
                await websocket.send(json.dumps({"type": "ping"}))
                pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"Ping response: {pong_response}")
                
                self.log_test_result("WebSocket Communication", True, f"Session {session_id}")
                return True
                
        except Exception as e:
            self.log_test_result("WebSocket Communication", False, f"Session {session_id}: {str(e)}")
            return False
    
    async def test_pickup_expired_orders(self):
        """Test /order/employment/courier/pickup-expired endpoint"""
        logger.info("=== Testing Pickup Expired Orders ===")
        
        # Test with valid operator_id
        payload = {"operator_id": 1}
        expected_fields = ["session_id", "success", "message", "sections", "total_sections"]
        
        response = await self.test_endpoint("/order/employment/courier/pickup-expired", payload, expected_fields)
        
        # Test WebSocket if session created
        if response.get("session_id") and response.get("total_sections", 0) > 0:
            test_messages = [
                {"type": "hardware_screen_ready"},
                {"type": "open_section", "section_id": 1},
                {"type": "storno"}
            ]
            await self.test_websocket_communication(response["session_id"], test_messages)
    
    async def test_pickup_employee_orders(self):
        """Test /order/employment/courier/pickup endpoint"""
        logger.info("=== Testing Pickup Employee Orders ===")
        
        # Test with valid operator_id
        payload = {"operator_id": 1}
        expected_fields = ["session_id", "success", "message", "sections", "total_sections"]
        
        response = await self.test_endpoint("/order/employment/courier/pickup", payload, expected_fields)
        
        # Test WebSocket if session created
        if response.get("session_id") and response.get("total_sections", 0) > 0:
            test_messages = [
                {"type": "hardware_screen_ready"},
                {"type": "open_section", "section_id": 1},
                {"type": "storno"}
            ]
            await self.test_websocket_communication(response["session_id"], test_messages)
    
    async def test_deliver_to_employee(self):
        """Test /order/employment/courier/deliver endpoint"""
        logger.info("=== Testing Deliver to Employee ===")
        
        # Test with valid phone numbers
        for phone in self.valid_phones:
            payload = {"phone_number": phone}
            expected_fields = ["session_id", "success", "message", "section_ids"]
            
            response = await self.test_endpoint("/order/employment/courier/deliver", payload, expected_fields)
            
            # Test WebSocket if session created
            if response.get("session_id") and response.get("success"):
                test_messages = [
                    {"type": "hardware_screen_ready"},
                    {"type": "open_section", "section_id": 1},
                    {"type": "inserted", "section_id": 1, "inserted": True},
                    {"type": "stop_selection"}
                ]
                await self.test_websocket_communication(response["session_id"], test_messages)
        
        # Test with invalid phone numbers
        for phone in self.invalid_phones:
            payload = {"phone_number": phone}
            expected_fields = ["session_id", "success", "message", "section_ids"]
            await self.test_endpoint("/order/employment/courier/deliver", payload, expected_fields)
    
    async def test_employee_send_order(self):
        """Test /order/employment/customer/send endpoint"""
        logger.info("=== Testing Employee Send Order ===")
        
        # Test with valid phone numbers
        for phone in self.valid_phones:
            payload = {"phone_number": phone}
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]
            
            response = await self.test_endpoint("/order/employment/customer/send", payload, expected_fields)
            
            # Test WebSocket if session created
            if response.get("session_id") and response.get("valid"):
                test_messages = [
                    {"type": "hardware_screen_ready"},
                    {"type": "open_section", "section_id": 1},
                    {"type": "inserted", "section_id": 1, "inserted": True},
                    {"type": "stop_selection"}
                ]
                await self.test_websocket_communication(response["session_id"], test_messages)
        
        # Test with invalid phone numbers
        for phone in self.invalid_phones:
            payload = {"phone_number": phone}
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]
            await self.test_endpoint("/order/employment/customer/send", payload, expected_fields)

    async def test_customer_pickup_order(self):
        """Test /order/employment/customer/pickup endpoint"""
        logger.info("=== Testing Customer Pickup Order ===")

        # Test with valid pickup pins
        for pin in self.valid_pickup_pins:
            payload = {"pickup_pin": pin}
            expected_fields = ["session_id", "success", "message", "section_id", "requires_payment", "amount"]

            response = await self.test_endpoint("/order/employment/customer/pickup", payload, expected_fields)

            # Test WebSocket if session created
            if response.get("session_id") and response.get("success"):
                test_messages = [
                    {"type": "hardware_screen_ready"},
                    {"type": "payment_completed"} if response.get("requires_payment") else {"type": "hardware_screen_ready"}
                ]
                await self.test_websocket_communication(response["session_id"], test_messages)

        # Test with invalid pickup pins
        for pin in self.invalid_pickup_pins:
            payload = {"pickup_pin": pin}
            expected_fields = ["session_id", "success", "message", "section_id", "requires_payment", "amount"]
            await self.test_endpoint("/order/employment/customer/pickup", payload, expected_fields)

    async def test_customer_reclaim_order(self):
        """Test /order/employment/customer/reclaim endpoint"""
        logger.info("=== Testing Customer Reclaim Order ===")

        # Test with valid reclamation pins
        for pin in self.valid_reclaim_pins:
            payload = {"reclamation_pin": pin}
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]

            response = await self.test_endpoint("/order/employment/customer/reclaim", payload, expected_fields)

            # Test WebSocket if session created
            if response.get("session_id") and response.get("valid"):
                test_messages = [
                    {"type": "hardware_screen_ready"},
                    {"type": "open_section", "section_id": 1},
                    {"type": "inserted", "section_id": 1, "inserted": True},
                    {"type": "stop_selection"}
                ]
                await self.test_websocket_communication(response["session_id"], test_messages)

        # Test with invalid reclamation pins
        for pin in self.invalid_reclaim_pins:
            payload = {"reclamation_pin": pin}
            expected_fields = ["session_id", "success", "section_id", "valid", "message"]
            await self.test_endpoint("/order/employment/customer/reclaim", payload, expected_fields)

    async def test_server_connectivity(self):
        """Test basic server connectivity"""
        logger.info("=== Testing Server Connectivity ===")

        try:
            response = requests.get(f"{self.base_url}/docs", timeout=5)
            if response.status_code == 200:
                self.log_test_result("Server Connectivity", True, "FastAPI docs accessible")
            else:
                self.log_test_result("Server Connectivity", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test_result("Server Connectivity", False, f"Connection failed: {str(e)}")

    async def run_all_tests(self):
        """Run all order module tests"""
        logger.info("🚀 Starting Order Module Tests")
        logger.info("=" * 50)

        # Test server connectivity first
        await self.test_server_connectivity()

        # Test all endpoints
        await self.test_pickup_expired_orders()
        await self.test_pickup_employee_orders()
        await self.test_deliver_to_employee()
        await self.test_employee_send_order()
        await self.test_customer_pickup_order()
        await self.test_customer_reclaim_order()

        # Print summary
        self.print_test_summary()

    def print_test_summary(self):
        """Print test results summary"""
        logger.info("=" * 50)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 50)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")

        if failed_tests > 0:
            logger.info("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["success"]:
                    logger.info(f"  - {result['test']}: {result['details']}")

        logger.info("=" * 50)


async def main():
    """Main test execution function"""
    import argparse

    parser = argparse.ArgumentParser(description="Order Module Testing Script")
    parser.add_argument("--host", default="localhost", help="Server host (default: localhost)")
    parser.add_argument("--port", default="8000", help="Server port (default: 8000)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    base_url = f"http://{args.host}:{args.port}"
    ws_url = f"ws://{args.host}:{args.port}"

    logger.info(f"Testing Order Module at {base_url}")
    logger.info(f"WebSocket URL: {ws_url}")

    tester = OrderModuleTester(base_url, ws_url)
    await tester.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {str(e)}")
        sys.exit(1)
