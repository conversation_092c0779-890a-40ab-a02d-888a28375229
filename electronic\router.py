import asyncio
import subprocess
import logging
import os
import json
import mysql.connector
from typing import List
from fastapi import APIRouter, HTTPException
from electronic.models import ElectronicCommandsResponse, ElectronicCommandRequest, ElectronicCommandResponse, CommandInfo
from config import device_config
from os import getenv

router = APIRouter()
logger = logging.getLogger(__name__)

# Path to boardctl.py
SCRIPT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
BOARDCTL_PATH = os.path.join(SCRIPT_DIR, "hardware", "boardctl.py")
PYTHON_ALIAS = "py"

def get_database_connection():
    """Get database connection using environment variables"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT", "3306")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )


async def fetch_commands_from_database() -> List[CommandInfo]:
    """Fetch commands from the database"""
    try:
        conn = get_database_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT name, params, description FROM commands ORDER BY name")
        rows = cursor.fetchall()

        commands = []
        for row in rows:
            # Parse the JSON params field
            try:
                params_data = json.loads(row['params'])
                # Handle different parameter formats
                if isinstance(params_data, list):
                    params = params_data
                elif isinstance(params_data, dict) and 'dynamic' in params_data:
                    # Handle dynamic parameters like {"dynamic":["box","color_name"]}
                    params = params_data['dynamic']
                else:
                    params = []
            except (json.JSONDecodeError, TypeError):
                params = []

            commands.append(CommandInfo(
                name=row['name'],
                params=params,
                description=row['description']
            ))

        cursor.close()
        conn.close()

        return commands

    except Exception as e:
        logger.error(f"Error fetching commands from database: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch commands from database")


@router.get("/get_commands", response_model=ElectronicCommandsResponse)
async def get_electronic_commands():
    """
    Returns all available electronic commands with their parameters.
    """
    try:
        commands = await fetch_commands_from_database()
        return ElectronicCommandsResponse(
            success=True,
            commands=commands
        )
    except Exception as e:
        logger.error(f"Error in get_electronic_commands: {str(e)}")
        return ElectronicCommandsResponse(
            success=False,
            commands=[]
        )


async def get_command_from_database(command_name: str) -> CommandInfo:
    """Get a specific command from the database"""
    try:
        conn = get_database_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT name, params, description FROM commands WHERE name = %s", (command_name,))
        row = cursor.fetchone()

        cursor.close()
        conn.close()

        if not row:
            return None

        # Parse the JSON params field
        try:
            params_data = json.loads(row['params'])
            # Handle different parameter formats
            if isinstance(params_data, list):
                params = params_data
            elif isinstance(params_data, dict) and 'dynamic' in params_data:
                # Handle dynamic parameters like {"dynamic":["box","color_name"]}
                params = params_data['dynamic']
            else:
                params = []
        except (json.JSONDecodeError, TypeError):
            params = []

        return CommandInfo(
            name=row['name'],
            params=params,
            description=row['description']
        )

    except Exception as e:
        logger.error(f"Error fetching command from database: {str(e)}")
        return None


@router.post("/command", response_model=ElectronicCommandResponse)
async def execute_electronic_command(request: ElectronicCommandRequest):
    """
    Execute an electronic command via boardctl.py
    """
    try:
        # Get command info from database
        command_info = await get_command_from_database(request.command_name)

        if not command_info:
            return ElectronicCommandResponse(
                success=False,
                result_code="-21",  # Invalid action error code
                error=f"Unknown command: {request.command_name}"
            )

        # Validate parameter count
        expected_param_count = len(command_info.params)
        if len(request.params) != expected_param_count:
            return ElectronicCommandResponse(
                success=False,
                result_code="-23",  # Invalid parameter count error code
                error=f"Command '{request.command_name}' expects {expected_param_count} parameters, got {len(request.params)}"
            )

        # Convert parameters to strings for boardctl.py
        validated_params = [str(param) for param in request.params]

        # Execute the command
        result_code, error_message = await execute_boardctl_command(request.command_name, validated_params)

        # Success is true only if result_code is "1"
        success = result_code == "1"

        return ElectronicCommandResponse(
            success=success,
            result_code=result_code,
            error=error_message if not success else None
        )

    except Exception as e:
        logger.error(f"Error executing command {request.command_name}: {str(e)}")
        return ElectronicCommandResponse(
            success=False,
            result_code="-22",  # Internal error code
            error=f"Internal error: {str(e)}"
        )


async def execute_boardctl_command(command_name: str, params: List[str]) -> tuple[str, str]:
    """
    Execute a boardctl.py command with the given parameters.
    Returns tuple of (result, error_message)
    """
    try:
        # Get hardware port from config
        hardware_port = device_config.fsm_config["hardware_port"]

        # Build command
        cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", command_name] + params

        logger.info(f"Executing command: {' '.join(cmd)}")

        # Execute command
        def run_command():
            try:
                proc = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=os.path.dirname(BOARDCTL_PATH)
                )
                stdout, stderr = proc.communicate(timeout=10)
                return stdout, stderr, proc.returncode
            except subprocess.TimeoutExpired:
                proc.kill()
                return b"Command timeout", b"", -1
            except Exception as e:
                return str(e).encode(), b"", -1

        loop = asyncio.get_running_loop()
        stdout, stderr, returncode = await loop.run_in_executor(None, run_command)

        stdout_text = stdout.decode().strip()
        stderr_text = stderr.decode().strip()

        logger.info(f"Command output - stdout: {stdout_text}")
        if stderr_text:
            logger.info(f"Command output - stderr: {stderr_text}")

        # Parse result and extract error messages
        lines = stdout_text.split('\n')

        # Look for error messages and result codes
        error_message = None
        result_code = None

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Check for common error patterns
            if "Cannot open port:" in line:
                error_message = line
            elif "Transmission Timeout Exception" in line:
                error_message = "Transmission Timeout Exception"
            elif "Invalid argument" in line:
                error_message = line
            elif "Hardware failure" in line:
                error_message = line
            elif "Invalid Action" in line:
                error_message = line
            elif "Script finished successfuly" in line:
                # Success indicator
                continue
            elif line.startswith('-'):
                # Error codes like -12, -22, etc.
                result_code = line
                # If we don't have an error message yet, look at the previous line
                if not error_message and i > 0:
                    prev_line = lines[i-1].strip()
                    if prev_line and not prev_line.startswith('-') and not prev_line.isdigit():
                        error_message = prev_line
            elif line == "1":
                # Success result code
                result_code = line
            elif line.isdigit():
                # Other numeric result
                result_code = line

        # Determine success based on result code
        if result_code == "1":
            # Success case
            return "1", None
        else:
            # Error case - return the error message if we found one
            if error_message:
                return result_code or "0", error_message
            elif result_code and result_code.startswith('-'):
                return result_code, f"Error code: {result_code}"
            elif stderr_text:
                return result_code or "0", stderr_text
            else:
                return result_code or "0", f"Command failed with return code {returncode}"

    except Exception as e:
        logger.error(f"Error executing boardctl command: {str(e)}")
        return "0", f"Execution error: {str(e)}"
