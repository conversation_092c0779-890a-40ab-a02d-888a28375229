# Order Module Testing Guide

## Overview
This guide explains how to test the order module functionality using the comprehensive test script.

## Test Script: `test_order_functionality.py`

### What it tests
- ✅ **Pickup Expired Orders** - `/order/employment/courier/pickup-expired`
- ✅ **Pickup Employee Orders** - `/order/employment/courier/pickup`  
- ✅ **Deliver to Employee** - `/order/employment/courier/deliver`
- ✅ **Employee Send Order** - `/order/employment/customer/send`
- ✅ **Customer Reclaim Order** - `/order/employment/customer/reclaim`
- ✅ **Customer Pickup Order** - `/order/employment/customer/pickup`
- ✅ **WebSocket Communication** - Session creation and messaging
- ✅ **Mock Response Validation** - Valid/invalid inputs
- ✅ **Response Structure** - All required fields present

## How to Run Tests

### 1. Prerequisites
```bash
# Install required Python packages
pip install requests websockets

# Ensure database is accessible and order_reservations table exists
```

### 2. Configure Environment
```bash
# Set EXTERNAL_API_ENABLE=false in .env file for mock responses
echo "EXTERNAL_API_ENABLE=false" >> .env
```

### 3. Start FastAPI Server
```bash
# Start the server in one terminal
uvicorn main:app --reload
```

### 4. Run Tests
```bash
# Run the test script in another terminal
python test_order_functionality.py
```

## Expected Output

### Successful Test Run
```
🚀 Starting Order Module Functionality Tests
============================================================
✅ FastAPI server is running

🔍 Testing Pickup Expired Orders...
✅ PASS | Pickup Expired - Valid Request | Found 0 sections
✅ PASS | Pickup Expired - WebSocket

🔍 Testing Pickup Employee Orders...
✅ PASS | Pickup Employee - Valid Request | Found 0 sections
✅ PASS | Pickup Employee - WebSocket

🔍 Testing Deliver to Employee...
✅ PASS | Deliver - Valid Phone | Phone validated successfully
✅ PASS | Deliver - WebSocket
✅ PASS | Deliver - Invalid Phone | Correctly rejected invalid phone

🔍 Testing Employee Send Order...
✅ PASS | Send - Valid Phone (Pre-reserved) | Section: 5
✅ PASS | Send - WebSocket
✅ PASS | Send - Valid Phone (No Pre-reservation) | No section pre-reserved
✅ PASS | Send - Invalid Phone | Correctly rejected invalid phone

🔍 Testing Customer Reclaim Order...
✅ PASS | Reclaim - Valid PIN (Pre-reserved) | Section: 7
✅ PASS | Reclaim - WebSocket
✅ PASS | Reclaim - Valid PIN (No Pre-reservation) | No section pre-reserved
✅ PASS | Reclaim - Invalid PIN | Correctly rejected invalid PIN

🔍 Testing Customer Pickup Order...
✅ PASS | Pickup - Invalid PIN | Correctly rejected invalid PIN

============================================================
📊 TEST SUMMARY
============================================================
Total Tests: 15
Passed: 15
Failed: 0
Success Rate: 100.0%

🎉 All tests passed! Order module is working correctly.
```

## Mock Response Test Data

### Valid Test Data (when EXTERNAL_API_ENABLE=false)

**Phone Numbers:**
- `123456789` - Valid with pre-reserved section
- `987654321` - Valid without pre-reserved section  
- `555666777` - Valid for delivery without pre-reserved section

**Reclamation PINs:**
- `RECLAIM123` - Valid with pre-reserved section
- `RECLAIM456` - Valid without pre-reserved section

**Invalid Data:**
- Any other phone number or PIN will be treated as invalid

## Troubleshooting

### Server Not Running
```
❌ Cannot connect to FastAPI server on localhost:8000
Please start the server with: uvicorn main:app --reload
```
**Solution:** Start the FastAPI server first.

### WebSocket Connection Failed
```
❌ FAIL | WebSocket | WebSocket error: ...
```
**Solution:** Check if WebSocket endpoint is properly configured and server supports WebSocket connections.

### Database Connection Issues
```
❌ FAIL | Database operations failing
```
**Solution:** Ensure database is running and order_reservations table exists.

### Mock Responses Not Working
```
❌ FAIL | External API calls failing
```
**Solution:** Set `EXTERNAL_API_ENABLE=false` in .env file.

## Test Coverage

The test script covers:
- ✅ All 6 order endpoints
- ✅ Valid and invalid input scenarios
- ✅ Mock response validation
- ✅ WebSocket session creation
- ✅ Response structure validation
- ✅ Error handling
- ✅ Pre-reserved vs non-reserved sections
- ✅ Phone number validation
- ✅ PIN validation

## Adding Custom Tests

To add more tests, extend the `OrderTester` class:

```python
def test_custom_scenario(self):
    """Test custom scenario"""
    data = {"custom_field": "value"}
    response = self.test_endpoint("/custom/endpoint", data=data)
    
    if response and response["success"]:
        self.log_test("Custom Test", True, "Custom test passed")
    else:
        self.log_test("Custom Test", False, "Custom test failed")
```

Then add it to `run_all_tests()` method.
