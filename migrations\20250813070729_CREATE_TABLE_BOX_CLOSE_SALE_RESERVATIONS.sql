-- migrate:up
CREATE TABLE sale_reservations (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    box_uuid VARCHAR(255) DEFAULT NULL,
    section_id VARCHAR(255) DEFAULT NULL,
    ean VA<PERSON>HAR(255) DEFAULT NULL,
    status INT(11) NOT NULL DEFAULT 0,
    name VARCHAR(255) DEFAULT NULL,
    description VARCHAR(255) DEFAULT NULL,
    quantity INT(11) DEFAULT 1,
    reserved INT(11) DEFAULT 0,
    reservation_pin VARCHAR(255) DEFAULT NULL,
    age_control_required TINYINT(4) DEFAULT 0,
    age_controlled TINYINT(4) NOT NULL DEFAULT 0,
    price DOUBLE NOT NULL DEFAULT 0,
    paid_status VARCHAR(255) DEFAULT NULL,
    type VARCHAR(256) NOT NULL DEFAULT 'ean',
    cover_image VARCHAR(255) DEFAULT NULL,
    last_update TIMES<PERSON>MP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE sale_reservations;
