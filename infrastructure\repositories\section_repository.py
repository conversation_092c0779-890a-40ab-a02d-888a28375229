import mysql.connector
import json
from os import getenv
from typing import List, Dict, Optional

class SectionRepository:
    def list_sections(self) -> List[Dict]:
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, size_width, size_depth, size_height, size_category
                FROM box_sections
                WHERE visible = 1
                ORDER BY section_id ASC
            """)
            sections = cursor.fetchall()

            # Add is_available field to each section
            for section in sections:
                section_id = section['section_id']
                identification_name = section['identification_name']
                section['is_available'] = self._check_section_availability(cursor, section_id, identification_name)

            return [dict(row) for row in sections]
        finally:
            cursor.close()
            conn.close()
            
    def get_section_data(self, section_id: str) -> Dict:
        """Vrátí data pro konkrétní section_id včetně visible flag"""
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, visible
                FROM box_sections
                WHERE section_id = %s
            """, (section_id,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            else:
                # Section neexistuje - vrátíme default data
                return {
                    "section_id": section_id,
                    "identification_name": None,
                    "tempered": 0,
                    "blocked": 0,
                    "service": 0,
                    "lock_id": None,
                    "led_section": None,
                    "mode": None,
                    "type": None,
                    "visible": 0
                }
        finally:
            cursor.close()

    def _check_section_availability(self, cursor, section_id: int, identification_name: str = None) -> bool:
        """Check if section is available (no active reservations)"""
        try:
            # Sections with identification_name == "stock" are always available
            if identification_name == "stock":
                return True

            # Check order_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM order_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            order_count = cursor.fetchone()['count']

            if order_count > 0:
                return False

            # Check sale_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM sale_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            sale_count = cursor.fetchone()['count']

            if sale_count > 0:
                return False

            # Check storage_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM storage_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            storage_count = cursor.fetchone()['count']

            return storage_count == 0

        except Exception as e:
            return False

    def get_box_layout(self) -> Optional[Dict]:
        """Vrátí layout z databáze podle posledního záznamu"""
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT box_layout
                FROM box_settings
                ORDER BY id DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result and result['box_layout']:
                return json.loads(result['box_layout'])
            return None
        finally:
            cursor.close()

section_repository = SectionRepository()
