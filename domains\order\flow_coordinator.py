"""
Order Flow Coordinator.
Orchestrates the entire order flow by combining flow engine and step handlers.
"""

import logging
from typing import Dict, Any

from .flow_engine import Order<PERSON>lowEngine
from .step_handlers import create_step_handler

logger = logging.getLogger(__name__)

class OrderFlowCoordinator:
    """Coordinates order flow execution and WebSocket communication"""
    
    def __init__(self):
        self.active_flows: Dict[str, OrderFlowEngine] = {}
        self.active_handlers: Dict[str, Dict[str, Any]] = {}  # session_id -> {current_handler, step_type}
        
    async def start_flow(self, session_id: str, flow_config: Dict[str, Any]) -> bool:
        """Start a new flow for a session"""
        try:
            logger.info(f"Starting order flow for session {session_id}")
            
            flow_engine = OrderFlowEngine()
            flow_engine.build_flow(flow_config)
            
            self.active_flows[session_id] = flow_engine
            self.active_handlers[session_id] = {"current_handler": None, "step_type": None}
            
            logger.info(f"Order flow created for session {session_id} with {len(flow_engine.steps)} steps")
            logger.info(f"Flow prepared for session {session_id}, waiting for WebSocket connection")
            return True
            
        except Exception as e:
            logger.error(f"Error starting order flow for session {session_id}: {e}")
            return False
    
    async def get_flow_status(self, session_id: str) -> Dict[str, Any]:
        """Get current flow status"""
        if session_id not in self.active_flows:
            return {"exists": False}
            
        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()
        
        return {
            "exists": True,
            "current_step": current_step["type"] if current_step else None,
            "completed_steps": flow_engine.completed_steps,
            "is_completed": flow_engine.is_completed()
        }
    
    async def execute_current_step(self, session_id: str) -> bool:
        """Execute the current step in the flow"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()
        
        if not current_step:
            logger.info(f"No current step to execute for session {session_id}")
            return True
            
        try:
            logger.info(f"Executing step '{current_step['type']}' for session {session_id}")
            
            handler = create_step_handler(current_step["type"], session_id)
            if not handler:
                logger.error(f"Failed to create handler for step type '{current_step['type']}'")
                return False
                
            # Store current handler
            self.active_handlers[session_id]["current_handler"] = handler
            self.active_handlers[session_id]["step_type"] = current_step["type"]
            
            # Execute step
            success = await handler.execute(current_step.get("context", {}))
            
            if success:
                logger.info(f"Step '{current_step['type']}' completed successfully for session {session_id}")
                return True
            else:
                logger.error(f"Step '{current_step['type']}' failed for session {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing step for session {session_id}: {e}")
            return False

    async def execute_current_step_async(self, session_id: str) -> bool:
        """Execute the current step in the flow asynchronously"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()
        
        if not current_step:
            logger.info(f"No more steps to execute for session {session_id}")
            return True
            
        try:
            logger.info(f"Executing step '{current_step['type']}' for session {session_id}")
            
            handler = create_step_handler(current_step["type"], session_id)
            if not handler:
                logger.error(f"Failed to create handler for step type '{current_step['type']}'")
                return False
                
            # Store current handler
            self.active_handlers[session_id]["current_handler"] = handler
            self.active_handlers[session_id]["step_type"] = current_step["type"]
            
            # Execute step
            success = await handler.execute(current_step.get("context", {}))

            if success is False:
                # Step is waiting for user interaction - don't move to next step yet
                logger.info(f"Step '{current_step['type']}' is waiting for user interaction for session {session_id}")
                return True
            elif success is None:
                # Step failed
                logger.error(f"Step '{current_step['type']}' failed for session {session_id}")
                return False

            logger.info(f"Step '{current_step['type']}' completed for session {session_id}")

        except Exception as e:
            logger.error(f"Error executing step for session {session_id}: {e}")
            return False

        # Move to next step
        flow_engine.complete_current_step()

        has_next = flow_engine.move_to_next_step()
        if has_next:
            next_step = flow_engine.get_current_step()
            logger.info(f"Moving to next step: {next_step['type']}")

            return await self.execute_current_step_async(session_id)
        else:
            logger.info(f"Flow completed for session {session_id}")
            await self.cleanup_flow(session_id)
            return True

    async def complete_current_step_and_continue(self, session_id: str) -> bool:
        """Complete the current step and move to the next step"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False

        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()

        if not current_step:
            logger.info(f"No current step to complete for session {session_id}")
            return True

        logger.info(f"Completing step '{current_step['type']}' for session {session_id}")

        # Move to next step
        flow_engine.complete_current_step()

        has_next = flow_engine.move_to_next_step()
        if has_next:
            next_step = flow_engine.get_current_step()
            logger.info(f"Moving to next step: {next_step['type']}")

            return await self.execute_current_step_async(session_id)
        else:
            logger.info(f"Flow completed for session {session_id}")
            await self.cleanup_flow(session_id)
            return True

    async def handle_websocket_message(self, session_id: str, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message from client"""
        logger.info(f"Handling WebSocket message for session {session_id}: {message.get('type', 'unknown')}")
        
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        message_type = message.get("type")
        
        # Handle flow control messages
        if message_type == "hardware_screen_ready":
            # Client is ready for hardware operations
            return await self.execute_current_step_async(session_id)
        elif message_type == "open_section":
            # Handle section opening request - delegate to current step handler
            section_id = message.get("section_id")
            logger.info(f"Section open request for section {section_id}")
            return await self._delegate_to_current_handler(session_id, message)
        elif message_type == "stop_selection":
            # Handle stop selection request - delegate to current step handler
            logger.info(f"Stop selection request for session {session_id}")
            return await self._delegate_to_current_handler(session_id, message)
        elif message_type == "inserted":
            # Handle insertion confirmation - delegate to current step handler
            logger.info(f"Insertion confirmation for session {session_id}")
            return await self._delegate_to_current_handler(session_id, message)
        elif message_type == "storno":
            # Handle cancellation
            logger.info(f"Flow cancelled for session {session_id}")
            await self.cleanup_flow(session_id)
            return True
        elif message_type == "selection_failed":
            # Handle selection failure
            logger.info(f"Selection failed for session {session_id}")
            return True
        else:
            logger.warning(f"Unknown message type '{message_type}' for session {session_id}")
            return False
    
    async def _delegate_to_current_handler(self, session_id: str, message: Dict[str, Any]) -> bool:
        """Delegate message to the current step handler"""
        try:
            handler_info = self.active_handlers.get(session_id)
            if not handler_info or not handler_info.get("current_handler"):
                logger.warning(f"No active handler for session {session_id}")
                return False

            current_handler = handler_info["current_handler"]

            # Check if handler has a handle_message method
            if hasattr(current_handler, 'handle_message'):
                return await current_handler.handle_message(message)
            else:
                logger.warning(f"Handler for session {session_id} doesn't support message handling")
                return False

        except Exception as e:
            logger.error(f"Error delegating message to handler for session {session_id}: {e}")
            return False

    async def cleanup_flow(self, session_id: str):
        """Clean up flow resources"""
        try:
            if session_id in self.active_flows:
                del self.active_flows[session_id]

            if session_id in self.active_handlers:
                del self.active_handlers[session_id]

            logger.info(f"Cleaned up flow for session {session_id}")

        except Exception as e:
            logger.error(f"Error cleaning up flow for session {session_id}: {e}")

# Global coordinator instance
flow_coordinator = OrderFlowCoordinator()
