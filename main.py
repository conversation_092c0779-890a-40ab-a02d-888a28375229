from fastapi import <PERSON><PERSON><PERSON>, APIRouter, WebSocket, Depends, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from contextlib import asynccontextmanager
from config import device_config
import mysql.connector
from os import getenv
from pydantic import BaseModel

# Initialize session manager cleanup on startup
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    if device_config.enabled_features.get("fsm"):
        from managers import session_manager, ws_manager
        await session_manager.start_cleanup_task()

    # Start Jetveo heartbeat service
    from infrastructure.external_apis.jetveo_client import start_jetveo_heartbeat
    start_jetveo_heartbeat()

    # Start MQTT service
    from mqtt.service import start_mqtt_service
    await start_mqtt_service()

    yield

    # Shutdown
    # Stop MQTT service
    from mqtt.service import stop_mqtt_service
    await stop_mqtt_service()

    # Stop Jetveo heartbeat service
    from infrastructure.external_apis.jetveo_client import stop_jetveo_heartbeat
    stop_jetveo_heartbeat()

    if device_config.enabled_features.get("fsm"):
        from managers import session_manager, ws_manager
        # Nejdřív ukončíme všechna WebSocket spojení
        for session_id, websocket in ws_manager.connections.copy().items():
            try:
                await websocket.close(code=1000)
                ws_manager.disconnect(session_id)
            except:
                pass

        # Pak ukončíme session manager
        await session_manager.stop_cleanup_task()

        # Vyčistíme všechny aktivní sessions
        for session_id in list(session_manager.active_sessions.keys()):
            await session_manager.remove_session(session_id)

# Create FastAPI app
app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create main router
main_router = APIRouter()

# Include the main router
app.include_router(main_router)

# Dynamically load enabled feature modules
if device_config.enabled_features.get("storage"):
    from storage.router import router as storage_router
    app.include_router(storage_router, prefix="/storage", tags=["storage"])

#if device_config.enabled_features.get("orders"):
#    from orders.router import router as orders_router
#    app.include_router(orders_router, prefix="/orders", tags=["orders"])

if device_config.enabled_features.get("sale"):
    from sale.router import router as sale_router
    app.include_router(sale_router)
    
    # Přidáme transaction router
    from transaction.router import router as transaction_router
    app.include_router(transaction_router, prefix="/transaction", tags=["transaction"])

if device_config.enabled_features.get("fsm"):
    # Add WebSocket endpoint for hardware
    from managers.websocket_handlers import hardware_websocket_endpoint
    
    @app.websocket("/ws/{session_id}")
    async def hardware_websocket_handler(websocket: WebSocket, session_id: str):
        await hardware_websocket_endpoint(websocket, session_id)

class OperatorSessionRequest(BaseModel):
    close: bool = False

# Operator FSM session creation endpoint - always available
@app.post("/operator/session")
async def create_operator_session(body: OperatorSessionRequest):
    """Vytvoří operátorskou session pro FSM sekvence s volbou čekání na zavření dvířek."""
    import uuid
    from managers.session_manager import session_manager, SessionType, SessionStatus
    close = body.close
    session_id = f"operator_{uuid.uuid4()}"  # Prefix pro operátora
    
    session = session_manager.create_session(
        session_id=session_id,
        session_type=SessionType.OPERATOR_FSM,
        status=SessionStatus.ACTIVE,
        expiry_minutes=15,
        session_data={"operator_close": close}
    )
    
    return {
        "success": True,
        "session_id": session_id,
        "websocket_url": f"/ws/{session_id}",
        "message": "Operator session created",
        "expires_at": session.expires_at.isoformat() if session.expires_at else None,
        "close": close
    }

from box.router import router as box_router
app.include_router(box_router, prefix="/box", tags=["box"])

# Include electronic router for electronic commands
from electronic.router import router as electronic_router
app.include_router(electronic_router, prefix="/electronic", tags=["electronic"])

# Include MQTT router
from mqtt.router import router as mqtt_router
app.include_router(mqtt_router, prefix="/mqtt", tags=["mqtt"])

# Include order router
from order.router import router as order_router
app.include_router(order_router, prefix="/order", tags=["order"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
