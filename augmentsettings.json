{"security.workspace.trust.untrustedFiles": "open", "security.allowedUNCHosts": ["desktop-9sfjve1"], "files.autoSave": "after<PERSON>elay", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "Git Bash": {"source": "WSL::bash"}, "Ubuntu (WSL)": {"path": "C:\\WINDOWS\\System32\\wsl.exe", "args": ["-d", "Ubuntu"]}}, "terminal.integrated.defaultProfile.windows": "Ubuntu (WSL)", "augment.advanced": {}}