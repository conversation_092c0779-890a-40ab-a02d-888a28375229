import mysql.connector
from typing import Optional
from os import getenv
from dotenv import load_dotenv
import logging
import json

load_dotenv()

logger = logging.getLogger(__name__)

def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

def log_sale_transaction(
    uuid: str,
    result: str,
    request: Optional[dict] = None,
    response: Optional[dict] = None,
    transaction_type: str = "sale",
    msg: Optional[str] = None
):
    """
    Logs a sale transaction to the sale_transactions table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()
    
    try:
        query = """
            INSERT INTO sale_transactions (
                uuid, type, msg, result, request, response
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = (
            uuid,
            transaction_type,
            msg,
            result,
            json.dumps(request) if request else None,
            json.dumps(response) if response else None
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged sale transaction: {uuid} - {result}")
        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging sale transaction: {err}")
        return None
    finally:
        cursor.close()
        conn.close()

def log_storage_transaction(
    uuid: str,
    result: str,
    request: Optional[dict] = None,
    response: Optional[dict] = None,
    transaction_type: str = "storage",
    msg: Optional[str] = None
):
    """
    Logs a storage transaction to the storage_transactions table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()
    
    try:
        query = """
            INSERT INTO storage_transactions (
                uuid, type, msg, result, request, response
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = (
            uuid,
            transaction_type,
            msg,
            result,
            json.dumps(request) if request else None,
            json.dumps(response) if response else None
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged storage transaction: {uuid} - {result}")
        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging storage transaction: {err}")
        return None
    finally:
        cursor.close()
        conn.close()
