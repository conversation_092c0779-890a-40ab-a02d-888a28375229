from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from os import getenv
from dotenv import load_dotenv

load_dotenv()

BEARER_TOKEN = getenv("API_BEARER_TOKEN")

class BearerAuth(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(<PERSON><PERSON><PERSON><PERSON>, self).__call__(request)
        if credentials:
            if credentials.credentials != BEARER_TOKEN:
                raise HTTPException(
                    status_code=403,
                    detail={
                        "success": False,
                        "error_code": "103"
                    }
                )
            return credentials.credentials
        else:
            raise HTTPException(
                status_code=403,
                detail={
                    "success": False,
                    "error_code": "103"
                }
            ) 