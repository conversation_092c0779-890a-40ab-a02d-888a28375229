"""
Order Step Handlers.
Handles individual steps in order flows.
"""

import logging
import async<PERSON>
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from managers.ws_manager import ws_manager
from hardware.locker_control import <PERSON>r<PERSON><PERSON>roller
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.section_repository import SectionRepository

logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)
    
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket connection is active"""
        return ws_manager.is_connected(self.session_id)
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            return False
    
    async def wait_for_websocket_ready(self, step_type: str) -> bool:
        """Wait for WebSocket ready signal"""
        try:
            # Send step ready signal
            await self.send_websocket_message({
                "type": "step_ready",
                "step": step_type,
                "message": f"Ready for {step_type}"
            })
            return True
        except Exception as e:
            logger.error(f"Error waiting for WebSocket ready: {e}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PickupLoopHandler(StepHandler):
    """Handler for pickup loop operations"""

    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.picking_up = False
        self.available_sections = []
        self.pickup_started = False

    async def handle_message(self, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message"""
        try:
            if not self.pickup_started:
                logger.warning(f"Received message before pickup started: {message.get('type')}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing pickup message: {message_type}")

            if message_type == "open_section":
                return await self._handle_pickup_section(message)
            elif message_type == "storno":
                return await self._handle_storno()
            else:
                logger.warning(f"Unknown message type in pickup loop: {message_type}")
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling message in pickup loop: {e}")
            await self.send_websocket_message({
                "type": "pickup_error",
                "message": f"Error in pickup: {str(e)}"
            })
            return False

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute pickup loop for multiple sections"""
        try:
            if not await self.wait_for_websocket_ready("pickup_loop"):
                return False

            self.available_sections = context.get('sections', [])
            operation = context.get('operation', 'pickup')

            await self.send_websocket_message({
                "type": "pickup_loop_started",
                "sections": self.available_sections,
                "operation": operation,
                "message": f"Starting pickup loop for {len(self.available_sections)} sections"
            })

            # Send initial status
            await self.send_websocket_message({
                "type": "pickup_status",
                "message": "Waiting for section selection. Send 'open_section' to pickup or 'storno' to cancel.",
                "available_sections": self.available_sections
            })

            self.picking_up = True
            self.pickup_started = True

            # Return False to indicate the step is not complete yet - it will complete when user cancels
            return False

        except Exception as e:
            logger.error(f"Error in pickup loop handler: {e}")
            await self.send_websocket_message({
                "type": "pickup_loop_error",
                "message": f"Error in pickup loop: {str(e)}"
            })
            return False

    async def _handle_pickup_section(self, message: Dict[str, Any]) -> bool:
        """Handle pickup section request with hardware integration"""
        try:
            section_id = message.get("section_id")
            if not section_id:
                await self.send_websocket_message({
                    "type": "error",
                    "message": "Section ID is required for open_section"
                })
                return False

            if section_id not in self.available_sections:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Section {section_id} is not available for pickup"
                })
                return False

            # Get hardware configuration for the section
            hardware_config = await self._get_hardware_config_for_section(int(section_id))
            if not hardware_config:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Hardware configuration not found for section {section_id}"
                })
                return False

            await self.send_websocket_message({
                "type": "section_opening",
                "section_id": section_id,
                "message": f"Opening section {section_id} for pickup"
            })

            # Start FSM sequence for this section
            from managers import sequence_manager

            success = await sequence_manager.start_fsm_sequence(
                session_id=self.session_id,
                sections=[hardware_config],
                pin="order_pickup",
                wait_for_close=True  # Wait for door to be closed after opening
            )

            if success:
                await self.send_websocket_message({
                    "type": "section_picked_up",
                    "section_id": section_id,
                    "message": f"Section {section_id} pickup completed. Send 'open_section' for more or 'storno' to finish."
                })

                # Update reservation status to completed
                await self._update_reservation_status(section_id)

                return True
            else:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Failed to open section {section_id}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling pickup section: {e}")
            await self.send_websocket_message({
                "type": "pickup_error",
                "message": f"Error picking up section: {str(e)}"
            })
            return False

    async def _handle_storno(self) -> bool:
        """Handle storno (cancel) message"""
        try:
            logger.info(f"Storno requested for session {self.session_id}")
            self.picking_up = False

            await self.send_websocket_message({
                "type": "pickup_cancelled",
                "message": "Pickup operation cancelled"
            })

            # Complete the step by notifying the flow coordinator
            await self._complete_step()
            return True

        except Exception as e:
            logger.error(f"Error handling storno: {e}")
            await self.send_websocket_message({
                "type": "pickup_error",
                "message": f"Error cancelling pickup: {str(e)}"
            })
            return False

    async def _update_reservation_status(self, section_id: int):
        """Update reservation status to completed after pickup"""
        try:
            from infrastructure.repositories.order_repository import OrderRepository
            repo = OrderRepository()

            # Find and update the reservation for this section
            # This is a simplified implementation - in real scenario you'd need to match by more criteria
            repo.update_reservation_status_by_section(section_id, 0)  # Mark as completed

        except Exception as e:
            logger.error(f"Error updating reservation status for section {section_id}: {e}")

    async def _complete_step(self):
        """Complete the current step and move to next step in flow"""
        from domains.order.flow_coordinator import flow_coordinator

        self.pickup_started = False
        self.picking_up = False

        # Notify the flow coordinator to complete this step and move to the next
        await flow_coordinator.complete_current_step_and_continue(self.session_id)

    async def _get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.
        Same implementation as in SectionSelectionHandler.
        """
        try:
            from managers.session_manager import SectionConfig
            import mysql.connector
            from os import getenv

            conn = mysql.connector.connect(
                host=getenv("DB_HOST"),
                port=int(getenv("DB_PORT")),
                database=getenv("DB_NAME"),
                user=getenv("DB_USER"),
                password=getenv("DB_PASSWORD")
            )

            cursor = conn.cursor(dictionary=True)

            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")

            query = """
                SELECT section_id, lock_id, is_tempered, led_section
                FROM box_sections
                WHERE box_uuid = %s AND section_id = %s
            """
            cursor.execute(query, (box_uuid, section_id))
            result = cursor.fetchone()

            cursor.close()
            conn.close()

            if result:
                return SectionConfig(
                    section_id=result['section_id'],
                    lock_id=result['lock_id'],
                    is_tempered=bool(result['is_tempered']),
                    led_section=result.get('led_section')
                )
            else:
                logger.warning(f"No hardware configuration found for section {section_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting hardware config for section {section_id}: {e}")
            return None

class SectionSelectionHandler(StepHandler):
    """Handler for section selection operations"""

    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.selecting = False
        self.selected_section_ids = []
        self.operation = None
        self.phone_number = None
        self.selection_started = False

    async def handle_message(self, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message"""
        try:
            if not self.selection_started:
                logger.warning(f"Received message before selection started: {message.get('type')}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing section selection message: {message_type}")

            if message_type == "open_section":
                return await self._handle_open_section(message)
            elif message_type == "stop_selection":
                return await self._handle_stop_selection()
            else:
                logger.warning(f"Unknown message type in section selection: {message_type}")
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling message in section selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute section selection - start the selection process"""
        try:
            if not await self.wait_for_websocket_ready("section_selection"):
                return False

            self.operation = context.get('operation')
            self.phone_number = context.get('phone_number')
            reserved_section_id = context.get('reserved_section_id')  # Pre-reserved section from jetveo (for employee_send)
            reserved_section_ids = context.get('reserved_section_ids')  # Pre-reserved sections from jetveo (for deliver_employee)

            await self.send_websocket_message({
                "type": "section_selection_started",
                "operation": self.operation,
                "reserved_section_id": reserved_section_id,
                "reserved_section_ids": reserved_section_ids,
                "message": "Starting section selection"
            })

            # Start the selection process - send initial status and wait for user interaction
            await self.send_websocket_message({
                "type": "selection_status",
                "message": "Waiting for section selection. Send 'open_section' to select or 'stop_selection' to finish.",
                "selected_sections": self.selected_section_ids
            })

            self.selecting = True
            self.selection_started = True

            # Return False to indicate the step is not complete yet - it will complete when user finishes selection
            return False

        except Exception as e:
            logger.error(f"Error in section selection handler: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def _handle_open_section(self, message: Dict[str, Any]) -> bool:
        """Handle open_section message with full hardware integration"""
        try:
            section_id = message.get("section_id")
            if not section_id:
                await self.send_websocket_message({
                    "type": "error",
                    "message": "Section ID is required for open_section"
                })
                return False

            # Get hardware configuration for the section
            hardware_config = await self._get_hardware_config_for_section(int(section_id))
            if not hardware_config:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Hardware configuration not found for section {section_id}"
                })
                return False

            await self.send_websocket_message({
                "type": "section_opening",
                "section_id": section_id,
                "message": f"Opening section {section_id}"
            })

            # Start FSM sequence for this section
            from managers import sequence_manager

            success = await sequence_manager.start_fsm_sequence(
                session_id=self.session_id,
                sections=[hardware_config],
                pin="order_selection",
                wait_for_close=True  # Wait for door to be closed after opening
            )

            if success:
                # Add to selected sections after successful hardware operation
                self.selected_section_ids.append(section_id)

                await self.send_websocket_message({
                    "type": "section_selected",
                    "section_id": section_id,
                    "selected_sections": self.selected_section_ids,
                    "message": f"Section {section_id} selected successfully. Send 'open_section' for more or 'stop_selection' to finish."
                })
                return True
            else:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Failed to open section {section_id}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling open_section: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error opening section: {str(e)}"
            })
            return False

    async def _handle_stop_selection(self) -> bool:
        """Handle stop_selection message"""
        try:
            logger.info(f"Stop selection requested for session {self.session_id}")
            self.selecting = False

            if self.selected_section_ids:
                # Create reservations for all selected sections
                success = await self._create_reservations()

                if success:
                    await self.send_websocket_message({
                        "type": "selection_completed",
                        "selected_sections": self.selected_section_ids,
                        "message": f"Selection completed with {len(self.selected_section_ids)} sections"
                    })

                    # Determine message type based on operation
                    if self.operation == "deliver_employee":
                        message_key = "order_deliver"
                    else:  # employee_send or customer_reclaim
                        message_key = "order_send"

                    await self.send_websocket_message({
                        message_key: True,
                        "section_ids": self.selected_section_ids,
                        "message": "Order processed successfully"
                    })
                else:
                    # Determine message type based on operation
                    if self.operation == "deliver_employee":
                        message_key = "order_deliver"
                    else:  # employee_send or customer_reclaim
                        message_key = "order_send"

                    await self.send_websocket_message({
                        message_key: False,
                        "message": "Failed to create reservations"
                    })

                # Complete the step by notifying the flow coordinator
                await self._complete_step()
                return True
            else:
                await self.send_websocket_message({
                    "type": "selection_cancelled",
                    "message": "Selection cancelled - no sections selected"
                })

                # Determine message type based on operation
                if self.operation == "deliver_employee":
                    message_key = "order_deliver"
                else:  # employee_send or customer_reclaim
                    message_key = "order_send"

                await self.send_websocket_message({
                    message_key: False,
                    "message": "Section selection failed"
                })

                # Complete the step by notifying the flow coordinator
                await self._complete_step()
                return True

        except Exception as e:
            logger.error(f"Error handling stop_selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error stopping selection: {str(e)}"
            })
            return False

    async def _get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.

        Args:
            section_id: The section ID to get configuration for

        Returns:
            SectionConfig object with hardware configuration or None if not found
        """
        try:
            from managers.session_manager import SectionConfig
            import mysql.connector
            from os import getenv

            conn = mysql.connector.connect(
                host=getenv("DB_HOST"),
                port=int(getenv("DB_PORT")),
                database=getenv("DB_NAME"),
                user=getenv("DB_USER"),
                password=getenv("DB_PASSWORD")
            )

            cursor = conn.cursor(dictionary=True)

            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")

            query = """
                SELECT section_id, lock_id, is_tempered, led_section
                FROM box_sections
                WHERE box_uuid = %s AND section_id = %s
            """
            cursor.execute(query, (box_uuid, section_id))
            result = cursor.fetchone()

            cursor.close()
            conn.close()

            if result:
                return SectionConfig(
                    section_id=result['section_id'],
                    lock_id=result['lock_id'],
                    is_tempered=bool(result['is_tempered']),
                    led_section=result.get('led_section')
                )
            else:
                logger.warning(f"No hardware configuration found for section {section_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting hardware config for section {section_id}: {e}")
            return None

    async def _create_reservations(self) -> bool:
        """Create reservations for selected sections"""
        try:
            from infrastructure.repositories.order_repository import OrderRepository

            repo = OrderRepository()
            all_results = []

            for section_id in self.selected_section_ids:
                if self.operation == "deliver_employee":
                    result = repo.create_employee_delivery_reservation(self.phone_number, section_id)
                elif self.operation == "employee_send":
                    result = repo.create_employee_send_reservation(self.phone_number, section_id)
                elif self.operation == "customer_reclaim":
                    result = repo.create_employee_send_reservation(self.phone_number, section_id)
                else:
                    result = {"success": False, "error": "Unknown operation"}

                all_results.append(result)

            # Check if all reservations were successful
            all_successful = all(result["success"] for result in all_results)
            return all_successful

        except Exception as e:
            logger.error(f"Error creating reservations: {e}")
            return False

    async def _complete_step(self):
        """Complete the current step and move to next step in flow"""
        from domains.order.flow_coordinator import flow_coordinator

        self.selection_started = False
        self.selecting = False

        # Notify the flow coordinator to complete this step and move to the next
        await flow_coordinator.complete_current_step_and_continue(self.session_id)



class HardwareHandler(StepHandler):
    """Handler for hardware operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute hardware operation"""
        try:
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            section_id = context.get('section_id')
            operation = context.get('operation', 'open_for_pickup')
            reservation_id = context.get('reservation_id')
            
            await self.send_websocket_message({
                "type": "hardware_started",
                "section_id": section_id,
                "operation": operation,
                "message": f"Starting hardware operation for section {section_id}"
            })
            
            # Open the section
            locker_controller = LockerController()
            success = await locker_controller.unlock_locker(section_id, mode="order")
            
            if success:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": True,
                    "section_id": section_id,
                    "message": f"Section {section_id} opened successfully"
                })
                
                # Update reservation status if this is a pickup
                if reservation_id:
                    repo = OrderRepository()
                    repo.update_reservation_status(reservation_id, 0)  # Mark as completed
            else:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": False,
                    "section_id": section_id,
                    "message": f"Failed to open section {section_id}"
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error in hardware handler: {e}")
            await self.send_websocket_message({
                "type": "hardware_error",
                "message": f"Error in hardware operation: {str(e)}"
            })
            return False

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "pickup_loop": PickupLoopHandler,
        "section_selection": SectionSelectionHandler,
        "hardware": HardwareHandler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
