"""
Permissions module for different operator roles.
Defines what actions and sections each role can access.
"""

from typing import Dict, List, Any, Optional
from os import getenv
import mysql.connector


permissions = {
    "manager": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "service": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "hygiene": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": False},
        "product_insert":  {"allowed": False},
        "manager_mode":    {"allowed": False},
        "delivery":        {"allowed": False},
    },
    "courier": {
        "service_open":    {"allowed": False},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "montage": {
        "service_open":    {"allowed": False},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    }
}


def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

def get_all_sections() -> List[int]:
    """
    Returns a list of all section IDs from box_sections table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT section_id
            FROM box_sections
            WHERE visible = 1
            ORDER BY section_id ASC
        """)
        sections = [row[0] for row in cursor.fetchall()]
        return sections
    except Exception as e:
        return []
    finally:
        cursor.close()
        conn.close()

def get_sections_with_active_reservations() -> List[int]:
    """
    Returns a list of section IDs that have active reservations (status=1) in sale_reservations.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id
            FROM sale_reservations
            WHERE status = 1
            ORDER BY section_id ASC
        """)
        sections = [row[0] for row in cursor.fetchall()]
        return sections
    except Exception as e:
        return []
    finally:
        cursor.close()
        conn.close()

def get_empty_sections() -> List[int]:
    """
    Returns a list of section IDs that are empty (no active reservations in either sale_reservations or storage_reservations).
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT section_id
            FROM box_sections
            WHERE visible = 1
            AND section_id NOT IN (
                SELECT DISTINCT CAST(section_id AS UNSIGNED)
                FROM sale_reservations
                WHERE status = 1
            )
            AND section_id NOT IN (
                SELECT DISTINCT CAST(section_id AS UNSIGNED)
                FROM storage_reservations
                WHERE status = 1
            )
            ORDER BY section_id ASC
        """)
        sections = [row[0] for row in cursor.fetchall()]
        return sections
    except Exception as e:
        return []
    finally:
        cursor.close()
        conn.close()

def get_hygiene_sections() -> List[int]:
    """
    Returns a list of sections available for hygiene.
    Reads comma separated string from HYGIENE_PERMITED_SECTIONS env variable.
    """
    hygiene_sections_str = getenv("HYGIENE_PERMITED_SECTIONS", "")

    if not hygiene_sections_str:
        return []

    return [int(section.strip()) for section in hygiene_sections_str.split(",")]

def update_hygiene():
    """Update hygiene permissions with available sections"""
    hygiene_sections = get_hygiene_sections()
    permissions["hygiene"]["collection"]["sections"] = hygiene_sections



def get_permissions_by_role(role: str) -> Optional[Dict[str, Any]]:
    """
    Get permissions for a specific role with dynamically populated sections.
    """
    if role not in permissions:
        return None

    # Make a deep copy of the permissions to avoid modifying the original
    import copy
    role_permissions = copy.deepcopy(permissions[role])

    # Update sections based on role and action
    match role:
        case "hygiene":
            # For hygiene, only service_open gets specific sections from env variable
            if role_permissions.get("service_open", {}).get("allowed"):
                hygiene_sections = get_hygiene_sections()
                role_permissions["service_open"]["sections"] = hygiene_sections

        case "manager" | "service" | "courier" | "montage":
            # For all other roles, service_open gets all sections
            if role_permissions.get("service_open", {}).get("allowed"):
                all_sections = get_all_sections()
                role_permissions["service_open"]["sections"] = all_sections

            # Collection gets sections with active reservations for all roles
            if role_permissions.get("collection", {}).get("allowed"):
                active_sections = get_sections_with_active_reservations()
                role_permissions["collection"]["sections"] = active_sections

            # Product insert gets empty sections for all roles
            if role_permissions.get("product_insert", {}).get("allowed"):
                empty_sections = get_empty_sections()
                role_permissions["product_insert"]["sections"] = empty_sections

    return role_permissions
