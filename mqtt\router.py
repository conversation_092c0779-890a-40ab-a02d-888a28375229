"""
MQTT Router for FastAPI endpoints
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
from .service import get_mqtt_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

def check_mqtt_availability():
    """Helper function to check if MQTT is available and connected"""
    mqtt_service = get_mqtt_service()

    if not mqtt_service.mqtt_enabled:
        raise HTTPException(status_code=503, detail="MQTT is disabled")

    if not mqtt_service.is_connected():
        raise HTTPException(status_code=503, detail="MQTT client not connected")

    return mqtt_service

class MQTTResponse(BaseModel):
    """Model for MQTT response messages"""
    status: str
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

class MQTTPublishRequest(BaseModel):
    """Model for publishing MQTT messages"""
    response: Dict[str, Any]

@router.get("/status")
async def get_mqtt_status():
    """Get MQTT service status"""
    try:
        mqtt_service = get_mqtt_service()
        status = mqtt_service.get_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting MQTT status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get MQTT status")

@router.post("/publish")
async def publish_mqtt_message(request: MQTTPublishRequest):
    """Publish a message via MQTT"""
    try:
        mqtt_service = check_mqtt_availability()

        # For general publish, use the general response topic
        mqtt_service.publish_general_response(request.response)

        return {
            "success": True,
            "message": "Message published successfully",
            "data": request.response
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error publishing MQTT message: {e}")
        raise HTTPException(status_code=500, detail="Failed to publish message")

@router.post("/test/electronic/section_open")
async def test_section_open():
    """Test endpoint for section_open command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test section_open response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test section_open: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test section_open")

@router.post("/test/electronic/check_doors")
async def test_check_doors():
    """Test endpoint for check_doors command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"door_state": "open", "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test check_doors response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test check_doors: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test check_doors")

@router.post("/test/electronic/unlock_service")
async def test_unlock_service():
    """Test endpoint for unlock_service command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test unlock_service response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test unlock_service: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test unlock_service")

@router.post("/test/electronic/check_service")
async def test_check_service():
    """Test endpoint for check_service command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"service_state": "locked", "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test check_service response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test check_service: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test check_service")

@router.post("/test/system/reboot_device")
async def test_reboot_device():
    """Test endpoint for reboot_device command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test reboot_device response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test reboot_device: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test reboot_device")

@router.post("/test/sale/edit_reservation")
async def test_sale_edit_reservation():
    """Test endpoint for sale edit_reservation command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test sale edit_reservation response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test sale edit_reservation: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test sale edit_reservation")

@router.post("/test/sale/reserve_product")
async def test_sale_reserve_product():
    """Test endpoint for sale reserve_product command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "reservation_pin": 123456, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test sale reserve_product response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test sale reserve_product: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test sale reserve_product")

@router.post("/test/sale/unreserve_product")
async def test_sale_unreserve_product():
    """Test endpoint for sale unreserve_product command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test sale unreserve_product response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test sale unreserve_product: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test sale unreserve_product")

@router.post("/test/storage/edit_reservation")
async def test_storage_edit_reservation():
    """Test endpoint for storage edit_reservation command"""
    try:
        mqtt_service = check_mqtt_availability()

        test_response = {"success": True, "test": True}
        mqtt_service.publish_general_response(test_response)

        return {"success": True, "message": "Test storage edit_reservation response sent", "data": test_response}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test storage edit_reservation: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test storage edit_reservation")

@router.get("/health")
async def mqtt_health_check():
    """Health check endpoint for MQTT service"""
    try:
        mqtt_service = get_mqtt_service()

        if not mqtt_service.mqtt_enabled:
            return {
                "healthy": False,
                "enabled": False,
                "initialized": False,
                "connected": False,
                "message": "MQTT is disabled",
                "timestamp": "2025-01-12T10:00:00Z"
            }

        is_healthy = mqtt_service.is_initialized and mqtt_service.is_connected()

        return {
            "healthy": is_healthy,
            "enabled": True,
            "initialized": mqtt_service.is_initialized,
            "connected": mqtt_service.is_connected(),
            "timestamp": "2025-01-12T10:00:00Z"
        }
    except Exception as e:
        logger.error(f"Error in MQTT health check: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": "2025-01-12T10:00:00Z"
        }
