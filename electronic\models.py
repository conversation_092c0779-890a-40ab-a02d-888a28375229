from pydantic import BaseModel
from typing import List, Union


class CommandInfo(BaseModel):
    """Model for individual command information"""
    name: str
    params: List[str]
    description: str


class ElectronicCommandsResponse(BaseModel):
    """Response model for /electronic/get_commands endpoint"""
    success: bool
    commands: List[CommandInfo]


class ElectronicCommandRequest(BaseModel):
    """Request model for /electronic/command endpoint"""
    command_name: str
    params: List[Union[str, int, float]]


class ElectronicCommandResponse(BaseModel):
    """Response model for /electronic/command endpoint"""
    success: bool
    result_code: str = None
    error: str = None
