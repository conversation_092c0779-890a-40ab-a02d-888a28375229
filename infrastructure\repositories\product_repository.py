"""
Product Repository - Database operations for product management.
Handles all database interactions for product operations.
"""

import logging
import mysql.connector
from typing import Dict, Optional, List, Any
from datetime import datetime
from uuid import uuid4
from decimal import Decimal
from os import getenv
from dotenv import load_dotenv
import random
from managers.timeline_logger import log_timeline_event

load_dotenv()

logger = logging.getLogger(__name__)

class ProductRepository:
    """Repository for product database operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def find_product_for_pickup(
        self, 
        section_id: Optional[int], 
        reservation_pin: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """
        Find product for pickup by section_id or reservation_pin.
        
        Args:
            section_id: Section ID to search in (customer pickup without PIN)
            reservation_pin: Reservation PIN to search by (customer pickup with PIN)
            
        Returns:
            Product record if found, None otherwise
            
        Note:
            - If product is reserved (reserved = 1), PIN is required
            - If product is not reserved, it can be picked up by section_id only
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            if reservation_pin is not None:
                # Find by reservation_pin (customer pickup with PIN)
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE reservation_pin = %s AND status = 1 AND reserved = 1
                """, (reservation_pin,))
            elif section_id is not None:
                # Find by section_id (customer pickup without PIN - only for non-reserved products)
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1 AND reserved = 0
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (str(section_id),))
            else:
                return None
                
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding product for pickup: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def find_reserved_product_in_section(self, section_id: int) -> Optional[Dict[str, Any]]:
        """
        Find reserved product in a specific section.
        
        Args:
            section_id: Section ID to search in
            
        Returns:
            Reserved product record if found, None otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
                ORDER BY created_at DESC
                LIMIT 1
            """, (str(section_id),))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding reserved product in section: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def check_section_has_active_product(self, section_id: int) -> bool:
        """
        Check if section already has an active product.
        
        Args:
            section_id: Section ID to check
            
        Returns:
            True if section has active product, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT id FROM sale_reservations 
                WHERE section_id = %s AND status != 0
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            return existing_product is not None
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error checking section: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def insert_custom_product(
        self, 
        section_id: int, 
        price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a custom product into database.
        
        Args:
            section_id: Section ID where product will be placed
            price: Product price
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            new_uuid = str(uuid4())
            
            # Insert new product
            cursor.execute("""
                INSERT INTO sale_reservations 
                (uuid, section_id, price, status, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'custom', %s, %s)
            """, (
                new_uuid,
                section_id,
                float(price),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            
            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (new_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting custom product: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def insert_product_with_ean(
        self, 
        section_id: int, 
        ean: str,
        product_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a product with EAN code into database.
        
        Args:
            section_id: Section ID where product will be placed
            ean: EAN code of the product
            product_data: Product data from external API
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            product_uuid = str(uuid4())
            
            # Insert new product into database with external API data
            cursor.execute("""
                INSERT INTO sale_reservations
                (uuid, section_id, ean, status, type, name, description, price,
                 age_control_required, cover_image, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'ean', %s, %s, %s, %s, %s, %s, %s)
            """, (
                product_uuid,
                str(section_id),
                ean,
                product_data.get('name'),
                product_data.get('description'),
                product_data.get('price'),
                product_data.get('age_control_required'),
                product_data.get('cover_image'),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            
            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (product_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting product with EAN: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def remove_product(self, section_id: int) -> bool:
        """
        Remove product from section.
        
        Args:
            section_id: Section ID to remove product from
            
        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                UPDATE sale_reservations 
                SET status = 0, last_update = %s
                WHERE section_id = %s AND status != 0
            """, (datetime.now(), section_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error removing product: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def remove_all_products(self) -> bool:
        """
        Remove all products from all sections.
        
        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                UPDATE sale_reservations 
                SET status = 0, last_update = %s
                WHERE status != 0
            """, (datetime.now(),))
            
            conn.commit()
            return True
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error removing all products: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def list_products(self) -> List[Dict[str, Any]]:
        """
        List all active products.
        
        Returns:
            List of active products
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE status != 0
                ORDER BY created_at DESC
            """)
            
            return cursor.fetchall()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error listing products: {err}")
            return []
        finally:
            cursor.close()
            conn.close()
    
    async def list_section_products(self, section_id: int) -> List[Dict[str, Any]]:
        """
        List products in specific section.
        
        Args:
            section_id: Section ID to list products from
            
        Returns:
            List of products in section
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE section_id = %s AND status != 0
                ORDER BY created_at DESC
            """, (section_id,))
            
            return cursor.fetchall()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error listing section products: {err}")
            return []
        finally:
            cursor.close()
            conn.close()
    
    async def reserve_section(
        self, 
        section_id: int, 
        reservation_pin: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Reserve a section with optional PIN.
        
        Args:
            section_id: Section ID to reserve
            reservation_pin: Optional reservation PIN
            
        Returns:
            Updated product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, check if there's a product with status=1 in the section
            cursor.execute("""
                SELECT id FROM sale_reservations 
                WHERE section_id = %s AND status = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No active product found in section {section_id}")
                return None
            
            # Generate 6-digit PIN if not provided
            if not reservation_pin:
                from .pin_generator import generate_pin
                reservation_pin = generate_pin()

                if reservation_pin is None:
                    self.logger.error("Failed to generate unique PIN for reservation")
                    return None
            
            self.logger.info(f"Generated PIN {reservation_pin} for section {section_id}")
            
            # Update product to reserved status
            cursor.execute("""
                UPDATE sale_reservations 
                SET reserved = 1, reservation_pin = %s, last_update = %s
                WHERE section_id = %s AND status = 1
            """, (reservation_pin, datetime.now(), section_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1
                """, (section_id,))
                
                result = cursor.fetchone()
                self.logger.info(f"Successfully reserved section {section_id} with PIN {reservation_pin}")
                return result
            else:
                self.logger.error(f"No rows updated when reserving section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error reserving section: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def purchase_product(
        self, 
        section_id: int, 
        reservation_pin: str
    ) -> Optional[Dict[str, Any]]:
        """
        Purchase a product using reservation PIN.
        
        Args:
            section_id: Section ID with the product
            reservation_pin: Reservation PIN
            
        Returns:
            Updated product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Check if there's a product with status=1 and matching PIN
            cursor.execute("""
                SELECT id, price, reserved, reservation_pin 
                FROM sale_reservations 
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            
            product = cursor.fetchone()
            if not product:
                return None
            
            # Check if product is reserved
            if not product['reserved']:
                return None
            
            # Verify PIN
            if product['reservation_pin'] != reservation_pin:
                log_timeline_event(
                    event_type="purchase_product",
                    event_result="failed",
                    section_id=str(section_id),
                    entered_pin=reservation_pin,
                    message="Invalid PIN for purchase"
                )
                return None
            
            # Update product status to purchased
            cursor.execute("""
                UPDATE sale_reservations 
                SET status = 2, paid_status = 'paid', last_update = %s
                WHERE id = %s
            """, (datetime.now(), product['id']))
            
            conn.commit()
            
            # Get the updated record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE id = %s
            """, (product['id'],))
            
            updated_product = cursor.fetchone()
            
            log_timeline_event(
                event_type="purchase_product",
                event_result="success",
                section_id=str(section_id),
                entered_pin=reservation_pin,
                message=f"Product purchased successfully for product ID: {product['id']}"
            )
            
            return updated_product
            
        except mysql.connector.Error as err:
            conn.rollback()
            log_timeline_event(
                event_type="purchase_product",
                event_result="failed",
                section_id=str(section_id),
                entered_pin=reservation_pin,
                message=f"Database error purchasing product: {err}"
            )
            self.logger.error(f"Database error purchasing product: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def update_payment_status(self, section_id: int) -> bool:
        """
        Update payment status for a product in section.
        After successful payment, set status to 0 (deactivated).
        
        Args:
            section_id: Section ID to update payment status for
            
        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                UPDATE sale_reservations 
                SET paid_status = '1',
                    status = 0,
                    last_update = %s
                WHERE section_id = %s
                  AND status = 1
            """, (datetime.now(), section_id))
            
            conn.commit()
            log_timeline_event(
                event_type="product_status_changed",
                event_result="sold",
                section_id=str(section_id),
                message="Payment status updated successfully"
            )
            return True
            
        except mysql.connector.Error as err:
            conn.rollback()
            log_timeline_event(
                event_type="update_product_status",
                event_result="failed",
                section_id=str(section_id),
                message=f"Database error updating payment status: {err}"
            )
            self.logger.error(f"Database error updating payment status: {err}")
            return False
        finally:
            cursor.close()
            conn.close()

    async def update_product_price(
        self, 
        section_id: int, 
        new_price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Update the price of a product in a specific section.
        
        Args:
            section_id: Section ID containing the product
            new_price: New price for the product
            
        Returns:
            Updated product record with old price or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, get the current product to check if it exists and get old price
            cursor.execute("""
                SELECT id, price FROM sale_reservations 
                WHERE section_id = %s AND status = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No active product found in section {section_id}")
                return None
            
            old_price = existing_product['price']
            
            # Update the price
            cursor.execute("""
                UPDATE sale_reservations 
                SET price = %s, last_update = %s
                WHERE section_id = %s AND status = 1
            """, (float(new_price), datetime.now(), section_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1
                """, (section_id,))
                
                result = cursor.fetchone()
                result['old_price'] = old_price  # Add old price to result
                
                self.logger.info(f"Successfully updated price in section {section_id} from {old_price} to {new_price}")
                return result
            else:
                self.logger.error(f"No rows updated when updating price in section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error updating product price: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def cancel_reservation(
        self, 
        section_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Cancel a reservation for a section.
        
        Args:
            section_id: Section ID to cancel reservation for
            
        Returns:
            Updated product record with cancelled PIN or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, check if there's a reserved product in the section
            cursor.execute("""
                SELECT id, reservation_pin FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No reserved product found in section {section_id}")
                return None
            
            cancelled_pin = existing_product['reservation_pin']
            
            # Cancel the reservation
            cursor.execute("""
                UPDATE sale_reservations 
                SET reserved = 0, reservation_pin = NULL, last_update = %s
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (datetime.now(), section_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1
                """, (section_id,))
                
                result = cursor.fetchone()
                result['cancelled_pin'] = cancelled_pin  # Add cancelled PIN to result
                
                self.logger.info(f"Successfully cancelled reservation in section {section_id}, PIN: {cancelled_pin}")
                return result
            else:
                self.logger.error(f"No rows updated when cancelling reservation in section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error cancelling reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

# Global repository instance
product_repository = ProductRepository()
