"""
Simple Product Flow Engine.
Builds workflow steps based on product data from sale_reservations table.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class FlowStep:
    """Single step in the product flow"""
    step_type: str  # "age_verification", "payment", "hardware"
    name: str
    config: Dict[str, Any] = field(default_factory=dict)
    required: bool = True

@dataclass 
class ProductFlowContext:
    """Context for a running product flow"""
    session_id: str
    product: Dict[str, Any]
    steps: List[FlowStep]
    current_step_index: int = 0
    step_data: Dict[str, Any] = field(default_factory=dict)
    completed_steps: List[str] = field(default_factory=list)
    started_at: datetime = field(default_factory=datetime.now)

class ProductFlowEngine:
    """Simple flow engine driven by database data"""
    
    def __init__(self):
        self.active_flows: Dict[str, ProductFlowContext] = {}
    
    async def start_pickup_flow(self, session_id: str, product: Dict[str, Any]) -> Dict[str, Any]:
        """Start pickup flow based on product properties from DB"""
        
        logger.info(f"Starting pickup flow for session {session_id}, product: {product.get('name', 'Unknown')}")
        
        # Build steps based on product data
        steps = self._build_steps_from_product_data(product)
        
        # Create flow context
        context = ProductFlowContext(
            session_id=session_id,
            product=product,
            steps=steps,
            current_step_index=0
        )
        
        # Store context
        self.active_flows[session_id] = context
        
        logger.info(f"Created flow with {len(steps)} steps for session {session_id}")
        
        return {
            "total_steps": len(steps),
            "current_step": steps[0].__dict__ if steps else None,
            "session_id": session_id
        }
    
    def _build_steps_from_product_data(self, product: Dict[str, Any]) -> List[FlowStep]:
        """Build flow steps based on sale_reservations table data"""
        
        steps = []
        
        # Step 1: Age verification (if required)
        if product.get('age_control_required'):
            steps.append(FlowStep(
                step_type="age_verification",
                name="Ověření věku",
                config={
                    "min_age": 18,  # Default or could be product-specific
                    "require_id_scan": bool(product.get('age_controlled', 0))
                },
                required=True
            ))
            logger.info(f"Added age verification step for product {product.get('id')}")
        
        # Step 2: Payment (if not paid)
        paid_status = product.get('paid_status')
        if not paid_status or paid_status != '1':
            price = float(product.get('price', 0))
            steps.append(FlowStep(
                step_type="payment", 
                name="Platba",
                config={
                    "amount": price,
                    "product_name": product.get('name', 'Produkt'),
                    "section_id": product.get('section_id')
                },
                required=True
            ))
            logger.info(f"Added payment step for product {product.get('id')}, amount: {price}")
        
        # Step 3: Hardware (always last)
        steps.append(FlowStep(
            step_type="hardware",
            name="Otevření schránky",
            config={
                "section_id": product.get('section_id'),
                "operation": "open_for_pickup"
            },
            required=True
        ))
        logger.info(f"Added hardware step for section {product.get('section_id')}")
        
        return steps
    
    def get_flow_context(self, session_id: str) -> Optional[ProductFlowContext]:
        """Get flow context for session"""
        return self.active_flows.get(session_id)
    
    def get_current_step(self, session_id: str) -> Optional[FlowStep]:
        """Get current step for session"""
        context = self.get_flow_context(session_id)
        if not context or context.current_step_index >= len(context.steps):
            return None
        return context.steps[context.current_step_index]
    
    def complete_current_step(self, session_id: str, step_data: Dict[str, Any] = None) -> Optional[FlowStep]:
        """Mark current step as completed and move to next"""
        context = self.get_flow_context(session_id)
        if not context:
            return None
        
        # Mark current step as completed
        if context.current_step_index < len(context.steps):
            current_step = context.steps[context.current_step_index]
            context.completed_steps.append(current_step.step_type)
            
            if step_data:
                context.step_data.update(step_data)
            
            logger.info(f"Completed step '{current_step.step_type}' for session {session_id}")
        
        # Move to next step
        context.current_step_index += 1
        
        # Return next step or None if finished
        if context.current_step_index < len(context.steps):
            next_step = context.steps[context.current_step_index]
            logger.info(f"Moving to next step '{next_step.step_type}' for session {session_id}")
            return next_step
        else:
            logger.info(f"Flow completed for session {session_id}")
            return None
    
    def is_flow_completed(self, session_id: str) -> bool:
        """Check if flow is completed"""
        context = self.get_flow_context(session_id)
        if not context:
            return True
        return context.current_step_index >= len(context.steps)
    
    def cleanup_flow(self, session_id: str):
        """Remove flow context"""
        if session_id in self.active_flows:
            del self.active_flows[session_id]
            logger.info(f"Cleaned up flow for session {session_id}")

class FlowEngine:
    """Simple flow engine for step-by-step execution"""
    
    def __init__(self):
        self.steps: List[Dict[str, Any]] = []
        self.current_step_index = 0
        self.completed_steps: List[str] = []
        
    def build_flow(self, flow_config: Dict[str, Any]):
        """Build flow steps from configuration"""
        logger.info(f"Building flow from config: {flow_config}")
        
        self.steps = []
        
        # Add age verification step if required
        if flow_config.get("requires_age_verification", False):
            self.steps.append({
                "type": "age_verification",
                "name": "Ověření věku",
                "context": {
                    "min_age": 18,
                    "product_id": flow_config.get("product_id")
                }
            })
            
        # Add payment step if required
        if flow_config.get("requires_payment", False):
            self.steps.append({
                "type": "payment", 
                "name": "Platba",
                "context": {
                    "amount": flow_config.get("amount", 0),
                    "section_id": flow_config.get("section_id"),
                    "product_id": flow_config.get("product_id")
                }
            })
            
        # Always add hardware step
        self.steps.append({
            "type": "hardware",
            "name": "Otevření schránky", 
            "context": {
                "section_id": flow_config.get("section_id"),
                "product_id": flow_config.get("product_id")
            }
        })
        
        logger.info(f"Built flow with {len(self.steps)} steps: {[s['type'] for s in self.steps]}")
    
    def get_current_step(self) -> Optional[Dict[str, Any]]:
        """Get current step"""
        if self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def complete_current_step(self) -> bool:
        """Mark current step as completed"""
        current_step = self.get_current_step()
        if current_step:
            self.completed_steps.append(current_step["type"])
            logger.info(f"Completed step '{current_step['type']}'")
            return True
        return False
    
    def move_to_next_step(self) -> bool:
        """Move to next step"""
        if self.current_step_index < len(self.steps) - 1:
            self.current_step_index += 1
            next_step = self.get_current_step()
            logger.info(f"Moved to step {self.current_step_index}: {next_step['type'] if next_step else 'None'}")
            return True
        return False

# Global instance
product_flow_engine = ProductFlowEngine() 