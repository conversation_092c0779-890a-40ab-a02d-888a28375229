"""
WebSocket Handler for Order Flow Sessions.
Handles WebSocket communication for flow-based order operations.
"""

import logging
import json
from fastapi import WebSocket, WebSocketDisconnect
import asyncio

from .flow_coordinator import flow_coordinator
from managers.ws_manager import ws_manager

logger = logging.getLogger(__name__)

async def handle_order_flow_websocket(
    websocket: WebSocket,
    session_id: str
):
    """Handle WebSocket communication for order flow sessions"""
    
    logger.info(f"Order flow WebSocket connected: {session_id}")
    
    try:
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for order flow session: {session_id}")
        
        # Get flow status
        flow_status = await flow_coordinator.get_flow_status(session_id)
        
        if not flow_status.get("exists", False):
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Order flow not found for this session"
            })
            return
        
        # Send initial flow status
        await ws_manager.send(session_id, {
            "type": "flow_status",
            "status": "connected",
            "current_step": flow_status.get("current_step"),
            "message": "Připojeno k order flow"
        })
        
        # Start flow execution now that WebSocket is connected
        logger.info(f"WebSocket connected, starting flow execution for session {session_id}")
        
        # Start step execution in background task
        asyncio.create_task(flow_coordinator.execute_current_step_async(session_id))
        
        logger.info(f"Starting message loop for session {session_id}")
        
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue
                
                try:
                    data = json.loads(message)
                    logger.info(f"Successfully parsed JSON from session {session_id}: {data}")
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON message format from session {session_id}: {e}, message: '{message}'")
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Neplatný formát zprávy (očekáván JSON)",
                        "error_code": "INVALID_JSON"
                    })
                    continue
                
                msg_type = data.get("type")
                
                logger.info(f"Processing order flow message type: {msg_type}")
                
                if msg_type == "ping":
                    await ws_manager.send(session_id, {
                        "type": "pong",
                        "message": "Connection alive"
                    })
                    continue
                
                # Handle flow-specific messages
                success = await flow_coordinator.handle_websocket_message(session_id, data)
                
                if not success:
                    logger.warning(f"Failed to handle message type '{msg_type}' for session {session_id}")
                    await ws_manager.send(session_id, {
                        "type": "message_error",
                        "message": f"Nepodařilo se zpracovat zprávu typu '{msg_type}'",
                        "original_message_type": msg_type
                    })
                
            except WebSocketDisconnect:
                logger.info(f"Order flow WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Chyba při zpracování zprávy: {str(e)}",
                    "error_code": "MESSAGE_PROCESSING_ERROR"
                })
                
    except WebSocketDisconnect:
        logger.info(f"Order flow WebSocket disconnected during setup: {session_id}")
    except Exception as e:
        logger.error(f"Error in order flow WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        await flow_coordinator.cleanup_flow(session_id)
        logger.info(f"Order flow WebSocket handler cleanup completed for session: {session_id}")
