-- migrate:up
CREATE TABLE storage_categories (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    size_category INT(11) NOT NULL,
    price INT(11) NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- migrate:down
DROP TABLE storage_categories;
