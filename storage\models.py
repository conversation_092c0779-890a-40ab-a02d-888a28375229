from pydantic import BaseModel
from typing import List, Optional
from sqlalchemy import <PERSON>umn, Integer, String, BigInteger, TIMESTAMP
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class StorageCategory(BaseModel):
    size_category: int
    price: int
    is_available: bool

class StorageCategoriesResponse(BaseModel):
    success: bool
    categories: List[StorageCategory]

class StorageInsertRequest(BaseModel):
    section_id: Optional[int] = None
    size_category: Optional[int] = None
    email: Optional[str] = None

class StoragePickupRequest(BaseModel):
    reservation_pin: str

class StorageSection(BaseModel):
    section_id: int
    identification_name: str
    tempered: int
    blocked: int
    service: int
    mode: str
    type: str
    size_width: int
    size_depth: int
    size_height: int
    size_category: int
    is_available: bool

class StorageSectionsResponse(BaseModel):
    success: bool
    products: List[StorageSection]
    total_count: int

class TimelineLog(Base):
    __tablename__ = 'timeline_log'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    serial_number = Column(String(255), nullable=False)
    entered_pin = Column(String(255), nullable=True)
    event_type = Column(String(50), nullable=True)
    event_result = Column(String(255), nullable=True)
    operator_id = Column(Integer, nullable=True)
    section_id = Column(String(255), nullable=True)
    tempered_unlock = Column(Integer, nullable=True)
    box_status = Column(String(255), nullable=True)
    message = Column(String(255), nullable=True)
    mode = Column(String(255), nullable=True)
    session_id = Column(String(50), nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
