from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from pydantic import Field

class TransactionState(Enum):
    INIT = "init"
    PAYMENT_PENDING = "payment_pending"
    PAYMENT_PROCESSING = "payment_processing"
    PAYMENT_COMPLETE = "payment_complete"
    HARDWARE_PREPARING = "hardware_preparing"
    HARDWARE_READY = "hardware_ready"
    HARDWARE_ACTIVE = "hardware_active"
    COMPLETE = "complete"
    ERROR = "error"

class TransactionType(Enum):
    PAYMENT = "payment"
    STORAGE = "storage"
    PICKUP = "pickup"
    MAINTENANCE = "maintenance"

class TransactionStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TransactionRequest(BaseModel):
    """Obecný request pro transakci"""
    transaction_type: TransactionType
    section_id: int
    amount: Optional[float] = None
    metadata: Dict[str, Any] = {}

class TransactionResponse(BaseModel):
    """Obecná odpověď pro transakci"""
    success: bool
    message: str
    transaction_session_id: Optional[str] = None
    websocket_url: Optional[str] = None
    requires_external_action: bool = False  # Např. platba kartou
    next_step: Optional[str] = None
    error_code: Optional[str] = None

@dataclass
class TransactionEvent:
    type: str
    timestamp: datetime
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@dataclass
class TransactionContext:
    session_id: str
    current_state: TransactionState
    transaction_type: TransactionType
    created_at: datetime
    last_updated: datetime
    payment_data: Optional[Dict[str, Any]] = None
    hardware_data: Optional[Dict[str, Any]] = None
    events: List[TransactionEvent] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.events is None:
            self.events = []

    def add_event(self, event_type: str, data: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
        event = TransactionEvent(
            type=event_type,
            timestamp=datetime.now(),
            data=data,
            error=error
        )
        self.events.append(event)
        self.last_updated = datetime.now()

class PaymentCallback(BaseModel):
    """Model pro callback z platebního terminálu"""
    status: str = Field(..., description="Status platby ('success' nebo 'fail')")
    msg: str = Field(..., description="Zpráva o výsledku platby")
    type: str = Field("payment", description="Typ callbacku")
    t: Optional[str] = Field(None, description="Timestamp z terminálu")
    auth_code: Optional[str] = Field(None, description="Autorizační kód platby")
    variable_symbol: Optional[str] = Field(None, description="Variable symbol sent with payment request for session identification")

class TransactionCallback(BaseModel):
    """Unified model pro všechny typy callbacků"""
    transaction_id: str = Field(..., description="ID transakce")
    status: str = Field(..., description="Status operace")
    type: str = Field(..., description="Typ operace")
    timestamp: datetime = Field(default_factory=datetime.now, description="Čas callbacku")
    message: Optional[str] = Field(None, description="Zpráva o výsledku")
    data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Dodatečná data")
    error: Optional[str] = Field(None, description="Chybová zpráva") 